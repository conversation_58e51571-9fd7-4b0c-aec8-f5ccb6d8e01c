import pandas as pd
import csv


def filter_csv_by_ami_list(csv_file_path, ami_list_file_path, output_file_path):
    """
    Filter CSV rows based on AMI IDs from a lookup file.

    Args:
        csv_file_path: Path to the input CSV file (output.csv)
        ami_list_file_path: Path to file containing AMI IDs (one per line)
        output_file_path: Path for the filtered output CSV file
    """

    # Read the AMI list and create a hashset for fast lookup
    ami_lookup_set = set()
    try:
        with open(ami_list_file_path, 'r') as ami_file:
            for line in ami_file:
                ami_id = line.strip()  # Remove whitespace and newlines
                if ami_id:  # Skip empty lines
                    ami_lookup_set.add(ami_id)

        print(
            f"Loaded {len(ami_lookup_set)} AMI IDs from {ami_list_file_path}")
        print(f"Sample AMI IDs: {list(ami_lookup_set)[:5]}")

    except FileNotFoundError:
        print(f"Error: AMI list file '{ami_list_file_path}' not found.")
        return
    except Exception as e:
        print(f"Error reading AMI list file: {e}")
        return

    # Read the CSV file and filter based on AMI lookup
    try:
        df = pd.read_csv(csv_file_path)
        print(f"Original CSV has {len(df)} rows")

        # Filter rows where imageId is in the AMI lookup set
        filtered_df = df[df['imageId'].isin(ami_lookup_set)]

        print(f"Filtered CSV will have {len(filtered_df)} rows")

        # Save the filtered results
        filtered_df.to_csv(output_file_path, index=False)
        print(f"Filtered results saved to: {output_file_path}")

        # Show some statistics
        if len(filtered_df) > 0:
            print(f"\nFiltered AMI IDs found:")
            ami_counts = filtered_df['imageId'].value_counts()
            print(ami_counts)
        else:
            print("No matching AMI IDs found in the CSV data.")

        return filtered_df

    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file_path}' not found.")
        return
    except Exception as e:
        print(f"Error processing CSV file: {e}")
        return

# Alternative implementation using built-in csv module (no pandas dependency)


def filter_csv_by_ami_list_builtin(csv_file_path, ami_list_file_path, output_file_path):
    """
    Filter CSV rows using built-in csv module (no pandas required).
    """

    # Read AMI list into a set
    ami_lookup_set = set()
    try:
        with open(ami_list_file_path, 'r') as ami_file:
            for line in ami_file:
                ami_id = line.strip()
                if ami_id:
                    ami_lookup_set.add(ami_id)

        print(
            f"Loaded {len(ami_lookup_set)} AMI IDs from {ami_list_file_path}")

    except FileNotFoundError:
        print(f"Error: AMI list file '{ami_list_file_path}' not found.")
        return

    # Process CSV file
    try:
        filtered_rows = []
        total_rows = 0

        with open(csv_file_path, 'r') as infile:
            reader = csv.DictReader(infile)
            headers = reader.fieldnames

            for row in reader:
                total_rows += 1
                if row.get('imageId') in ami_lookup_set:
                    filtered_rows.append(row)

        print(f"Original CSV has {total_rows} rows")
        print(f"Filtered CSV will have {len(filtered_rows)} rows")

        # Write filtered results
        with open(output_file_path, 'w', newline='') as outfile:
            if filtered_rows:
                writer = csv.DictWriter(outfile, fieldnames=headers)
                writer.writeheader()
                writer.writerows(filtered_rows)
            else:
                # Write empty CSV with headers
                writer = csv.DictWriter(outfile, fieldnames=headers)
                writer.writeheader()

        print(f"Filtered results saved to: {output_file_path}")

    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file_path}' not found.")
        return
    except Exception as e:
        print(f"Error processing CSV file: {e}")
        return


# Usage example
if __name__ == "__main__":
    # File paths
    csv_input = "RESULTS-ec2-IP.csv"
    ami_list = "imagebuilder-amis.txt"
    csv_output = "RESULTS-ec2-IP-unmanaged.csv"

    # Using pandas version (recommended)
    filtered_data = filter_csv_by_ami_list(csv_input, ami_list, csv_output)

    # Uncomment to use the built-in csv version instead:
    # filter_csv_by_ami_list_builtin(csv_input, ami_list, csv_output)

    # Optional: Display the filtered results
    if filtered_data is not None and len(filtered_data) > 0:
        print(f"\nFiltered Results Preview:")
        print(filtered_data.to_string(index=False))
