#!/usr/bin/env python3
"""
Debug script to check AWS access and Image Builder permissions
"""

import boto3
import json
from botocore.exceptions import ClientError, NoCredentialsError

def test_aws_access():
    """Test basic AWS access"""
    try:
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"✓ AWS Access OK")
        print(f"  Account: {identity.get('Account')}")
        print(f"  User/Role: {identity.get('Arn')}")
        return True
    except NoCredentialsError:
        print("✗ No AWS credentials found")
        return False
    except ClientError as e:
        print(f"✗ AWS access error: {e}")
        return False

def test_imagebuilder_access(region='us-east-1'):
    """Test Image Builder service access"""
    try:
        client = boto3.client('imagebuilder', region_name=region)
        
        # Test list-images permission
        try:
            response = client.list_images(maxResults=1)
            print(f"✓ Image Builder list-images access OK in {region}")
            image_count = len(response.get('imageVersionList', []))
            print(f"  Found {image_count} images")
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'AccessDenied':
                print(f"✗ Access denied to Image Builder in {region}")
            else:
                print(f"✗ Image Builder error in {region}: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Image Builder client error in {region}: {e}")
        return False

def test_ec2_access(region='us-east-1'):
    """Test EC2 access for AMI queries"""
    try:
        client = boto3.client('ec2', region_name=region)
        
        # Test describe-images with a simple filter
        try:
            response = client.describe_images(
                Owners=['self'],
                MaxResults=1
            )
            print(f"✓ EC2 describe-images access OK in {region}")
            ami_count = len(response.get('Images', []))
            print(f"  Found {ami_count} AMIs owned by this account")
            return True
        except ClientError as e:
            print(f"✗ EC2 access error in {region}: {e}")
            return False
            
    except Exception as e:
        print(f"✗ EC2 client error in {region}: {e}")
        return False

def test_cross_account_access():
    """Test cross-account role assumption"""
    try:
        # Read first account from accounts.txt
        with open('accounts.txt', 'r') as f:
            first_line = f.readline().strip()
            if first_line:
                account_id, account_name = first_line.split('\t')
                print(f"\nTesting cross-account access to: {account_name} ({account_id})")
                
                sts = boto3.client('sts')
                role_arn = f"arn:aws:iam::{account_id}:role/OrganizationAccountAccessRole"
                
                try:
                    response = sts.assume_role(
                        RoleArn=role_arn,
                        RoleSessionName="ImageBuilderTest"
                    )
                    print(f"✓ Successfully assumed role in {account_id}")
                    
                    # Test Image Builder access with assumed role
                    credentials = response['Credentials']
                    session = boto3.Session(
                        aws_access_key_id=credentials['AccessKeyId'],
                        aws_secret_access_key=credentials['SecretAccessKey'],
                        aws_session_token=credentials['SessionToken']
                    )
                    
                    ib_client = session.client('imagebuilder', region_name='us-east-1')
                    ib_response = ib_client.list_images(maxResults=1)
                    print(f"✓ Image Builder access OK in cross-account {account_id}")
                    
                    return True
                    
                except ClientError as e:
                    print(f"✗ Cross-account access error: {e}")
                    return False
                    
    except FileNotFoundError:
        print("✗ accounts.txt file not found")
        return False
    except Exception as e:
        print(f"✗ Cross-account test error: {e}")
        return False

def main():
    print("=== AWS Access Debug ===")
    
    # Test basic AWS access
    if not test_aws_access():
        print("\nPlease configure AWS credentials first:")
        print("  aws configure")
        print("  or set AWS_PROFILE environment variable")
        return
    
    print()
    
    # Test Image Builder access
    regions = ['us-east-1', 'us-west-2', 'eu-west-1']
    for region in regions:
        test_imagebuilder_access(region)
        test_ec2_access(region)
        print()
    
    # Test cross-account access
    test_cross_account_access()
    
    print("\n=== Debug Complete ===")

if __name__ == "__main__":
    main()
