import pandas as pd

vms_with_amis = pd.read_csv('RESULTS-ec2-IP.csv')
vms_with_depts = pd.read_csv('aws-vms.csv')
# Read AMI IDs into a set for fast lookup
with open('imagebuilder-amis.txt', 'r') as f:
    amis_list = {line.strip() for line in f if line.strip()}

vms_with_amis['imageId'] = vms_with_amis['imageId'].astype(str)

merged_df = pd.merge(
    vms_with_amis,
    vms_with_depts,
    left_on='resourceId',
    right_on='Identifier',
    how='left'
)

# Add boolean column to check if AMI was created by CCOE
merged_df['ami_created_by_ccoe'] = merged_df['imageId'].isin(amis_list)

merged_df.to_csv('AWS_all_vms_with_ami_validated.csv', index=False)

