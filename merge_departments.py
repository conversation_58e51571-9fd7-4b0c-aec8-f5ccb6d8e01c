import pandas as pd

vms_with_amis = pd.read_csv('RESULTS-ec2-IP-july21.csv')
vms_with_depts = pd.read_csv('aws-vms.csv')
# Read AMI IDs into a set for fast lookup
with open('imagebuilder-amis.txt', 'r') as f:
    amis_list = {line.strip() for line in f if line.strip()}

vms_with_amis['imageId'] = vms_with_amis['imageId'].astype(str)


# merged_df = pd.merge(
#     vms_with_amis,
#     vms_with_depts,
#     left_on='resourceId',
#     right_on='Identifier',
#     how='outer'
# )

# # Drop multiple columns
# merged_df = merged_df.drop(['instanceType', 'tenancy', 'ARN', 
#                             'CCoE_Product_Name','_Platform OS version', 'privateIpAddress'], axis=1)

# # Add boolean column to check if AMI was created by CCOE
# merged_df['ami_created_by_ccoe'] = merged_df['imageId'].isin(amis_list)

# merged_df.to_csv('AWS_all_vms_with_ami_validated.csv', index=False)


#The below portion is only because vms_with_depts has mismatching data. Only using Account owner information
aws_accounts = vms_with_depts[['AWS_Account' ,'Department' ,'Owned by', 'Managed by']]
aws_accounts = aws_accounts.drop_duplicates()
aws_accounts.to_csv('aws-accounts.csv', index=False)

vms_with_amis['accountId'] = vms_with_amis['accountId'].astype(str)
aws_accounts['AWS_Account'] = aws_accounts['AWS_Account'].astype(str)

merged_df2 = pd.merge(
    vms_with_amis,
    aws_accounts,
    left_on='accountId',
    right_on='AWS_Account',
    how='left'
)

merged_df2 = merged_df2.drop(['instanceType', 'tenancy'], axis=1)
                            
merged_df2['ami_created_by_ccoe'] = merged_df2['imageId'].isin(amis_list)
merged_df2.to_csv('AWS-config-vms-with-ami-validated.csv', index=False)