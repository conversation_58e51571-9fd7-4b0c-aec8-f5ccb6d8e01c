import json
import csv
import pandas as pd


def json_to_csv(json_file_path, csv_file_path):
    # Read the JSON file
    with open(json_file_path, 'r') as file:
        data = json.load(file)

    # Parse the JSON strings in the Results array
    parsed_results = []
    for result_string in data['Results']:
        parsed_result = json.loads(result_string)
        parsed_results.append(parsed_result)

    # Flatten the nested structure for CSV
    flattened_data = []
    for item in parsed_results:
        flat_item = {
            'accountId': item.get('accountId'),
            'resourceId': item.get('resourceId'),
            'resourceType': item.get('resourceType'),
            'imageId': item.get('configuration', {}).get('imageId'),
            'instanceType': item.get('configuration', {}).get('instanceType'),
            'tenancy': item.get('configuration', {}).get('placement', {}).get('tenancy'),
            'privateIpAddress': item.get('configuration', {}).get('networkInterfaces', [{}])[0].get('privateIpAddress')
        }
        flattened_data.append(flat_item)

    # Write to CSV using pandas (easier approach)
    df = pd.DataFrame(flattened_data)
    df.to_csv(csv_file_path, index=False)

    print(f"CSV file created: {csv_file_path}")
    return df

# Alternative approach using built-in csv module


def json_to_csv_builtin(json_file_path, csv_file_path):
    with open(json_file_path, 'r') as file:
        data = json.load(file)

    # Parse JSON strings and flatten
    parsed_results = []
    for result_string in data['Results']:
        parsed_result = json.loads(result_string)
        parsed_results.append(parsed_result)

    # Define CSV headers
    headers = ['accountId', 'resourceId', 'resourceType',
               'imageId', 'instanceType', 'tenancy', 'privateIpAddress']

    with open(csv_file_path, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers)
        writer.writeheader()

        for item in parsed_results:
            row = {
                'accountId': item.get('accountId'),
                'resourceId': item.get('resourceId'),
                'resourceType': item.get('resourceType'),
                'imageId': item.get('configuration', {}).get('imageId'),
                'instanceType': item.get('configuration', {}).get('instanceType'),
                'tenancy': item.get('configuration', {}).get('placement', {}).get('tenancy'),
                'privateIpAddress': item.get('configuration', {}).get('networkInterfaces', [{}])[0].get('privateIpAddress')
            }
            writer.writerow(row)

    print(f"CSV file created: {csv_file_path}")


# Usage examples:
if __name__ == "__main__":
    # Using pandas (recommended)
    df = json_to_csv('RESULTS-ec2-IP.json', 'RESULTS-ec2-IP.csv')
    print(df.head())
