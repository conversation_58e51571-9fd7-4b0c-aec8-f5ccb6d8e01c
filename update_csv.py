import csv

# File paths
current_file = "RESULTS-ec2-IP-unmanaged-2.csv"
aws_vms_file = "aws-vms.csv"
output_file = "RESULTS-ec2-IP-unmanaged-2.updated.csv"

# Read aws-vms.csv into a dict: accountId -> first 5 columns
aws_vms_map = {}
with open(aws_vms_file, newline='', encoding='utf-8') as f:
    reader = csv.reader(f)
    aws_vms_header = next(reader)
    for row in reader:
        if len(row) < 6:
            continue
        account_id = row[0].strip()
        if account_id and account_id not in aws_vms_map:
            aws_vms_map[account_id] = row[:5]

# Read current file, update first 5 columns if match found
with open(current_file, newline='', encoding='utf-8') as fin, \
     open(output_file, 'w', newline='', encoding='utf-8') as fout:
    reader = csv.reader(fin)
    writer = csv.writer(fout)
    header = next(reader)
    writer.writerow(header)
    for row in reader:
        if len(row) < 6:
            writer.writerow(row)
            continue
        account_id = row[5].strip()
        if account_id in aws_vms_map:
            row[:5] = aws_vms_map[account_id]
        writer.writerow(row)

print(f"Updated file written to {output_file}")