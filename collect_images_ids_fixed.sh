#!/bin/bash

# Fixed script to collect all Image Builder AMI IDs from all regions
# SECURITY: Use AWS profiles or environment variables instead of hardcoded credentials

set -e  # Exit on any error

aws sso login --profile nxpshared

# Configuration
OUTPUT_FILE="imagebuilder-amis.txt"
TEMP_FILE="${OUTPUT_FILE}.tmp"

# Check required tools
command -v aws >/dev/null 2>&1 || { echo "Error: aws CLI not found"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo "Error: jq not found"; exit 1; }

# Use AWS profile instead of hardcoded credentials
# export AWS_PROFILE=your-profile-name
# Or ensure AWS credentials are set via environment or IAM role

echo "=== Collecting Image Builder AMI IDs ==="
echo "Output file: $OUTPUT_FILE"

# Get AWS regions (cache for efficiency)
echo "Getting AWS regions..."
REGIONS=$(aws ec2 describe-regions --profile nxpshared --query 'Regions[].RegionName' --output text 2>/dev/null || echo "us-east-1 us-west-2 eu-west-1")

# Initialize output file (empty, no blank line)
> "$OUTPUT_FILE"

# Process each region
for REGION in $REGIONS; do
    echo "Processing region: $REGION..."
    
    # Check if Image Builder is available in this region
    if ! aws imagebuilder list-images --profile nxpshared --region "$REGION" >/dev/null 2>&1; then
        echo "  Skipping $REGION (Image Builder not available or access denied)"
        continue
    fi
    
    # Initialize pagination
    NEXT_TOKEN=""
    PAGE_COUNT=0
    
    # Loop through all pages of images
    while true; do
        ((PAGE_COUNT++))
        echo "  Page $PAGE_COUNT..."
        
        # Build command with or without next-token
        if [ -z "$NEXT_TOKEN" ]; then
            RESPONSE=$(aws imagebuilder list-images --profile nxpshared --region "$REGION" --output json 2>/dev/null || echo '{"imageVersionList":[]}')
        else
            RESPONSE=$(aws imagebuilder list-images --profile nxpshared --region "$REGION" --next-token "$NEXT_TOKEN" --output json 2>/dev/null || echo '{"imageVersionList":[]}')
        fi
        
        # Check if response is valid
        if ! echo "$RESPONSE" | jq empty 2>/dev/null; then
            echo "  Error: Invalid JSON response from AWS API"
            break
        fi
        
        # Extract image ARNs from current page
        IMAGE_ARNS=$(echo "$RESPONSE" | jq -r '.imageVersionList[]?.arn // empty')
        
        if [ -z "$IMAGE_ARNS" ]; then
            echo "  No images found on this page"
        else
            # Process each image ARN
            echo "$IMAGE_ARNS" | while IFS= read -r IMAGE_ARN; do
                if [ -n "$IMAGE_ARN" ]; then
                    echo "    Processing: $(basename "$IMAGE_ARN")"
                    
                    # Get image details and extract AMI IDs
                    IMAGE_DETAILS=$(aws imagebuilder get-image --profile nxpshared --image-build-version-arn "$IMAGE_ARN" --region "$REGION" --output json 2>/dev/null || echo '{}')
                    
                    # Extract all AMI IDs (don't filter by region to catch cross-region AMIs)
                    AMI_IDS=$(echo "$IMAGE_DETAILS" | jq -r '.image.outputResources.amis[]?.image // empty' 2>/dev/null)
                    
                    if [ -n "$AMI_IDS" ]; then
                        echo "$AMI_IDS" | while IFS= read -r AMI_ID; do
                            if [ -n "$AMI_ID" ]; then
                                echo "$AMI_ID" >> "$OUTPUT_FILE"
                            fi
                        done
                    fi
                fi
            done
        fi
        
        # Check if there's a next token
        NEXT_TOKEN=$(echo "$RESPONSE" | jq -r '.nextToken // empty')
        
        # Break if no more pages
        if [ -z "$NEXT_TOKEN" ]; then
            echo "  Completed region $REGION"
            break
        fi
    done
done

# Remove duplicates and empty lines, sort results
echo "Processing results..."
if [ -s "$OUTPUT_FILE" ]; then
    sort "$OUTPUT_FILE" | uniq | grep -v '^[[:space:]]*$' > "$TEMP_FILE"
    mv "$TEMP_FILE" "$OUTPUT_FILE"
    
    TOTAL_AMIS=$(wc -l < "$OUTPUT_FILE")
    echo "✓ Done. AMI IDs saved to $OUTPUT_FILE"
    echo "✓ Total unique AMIs found: $TOTAL_AMIS"
    
    if [ "$TOTAL_AMIS" -gt 0 ]; then
        echo ""
        echo "First 5 AMI IDs:"
        head -5 "$OUTPUT_FILE"
    fi
else
    echo "⚠ No AMI IDs found"
    rm -f "$OUTPUT_FILE"
fi

echo ""
echo "=== Collection Complete ==="
