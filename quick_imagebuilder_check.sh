#!/bin/bash
# Quick script to check Image Builder images in current account/region

set -e

REGION=${1:-"us-east-1"}
echo "=== Quick Image Builder Check ==="
echo "Region: $REGION"
echo "Account: $(aws sts get-caller-identity --query Account --output text 2>/dev/null || echo 'Unknown')"
echo ""

echo "1. Image Builder Images:"
echo "========================"
aws imagebuilder list-images --region "$REGION" --output table --query 'imageVersionList[*].[name,version,platform,dateCreated,owner]' 2>/dev/null || echo "No Image Builder images found or access denied"

echo ""
echo "2. Image Builder Pipelines:"
echo "=========================="
aws imagebuilder list-image-pipelines --region "$REGION" --output table --query 'imagePipelineList[*].[name,status,platform,dateCreated]' 2>/dev/null || echo "No Image Builder pipelines found or access denied"

echo ""
echo "3. Image Builder Recipes:"
echo "========================"
aws imagebuilder list-image-recipes --region "$REGION" --output table --query 'imageRecipeList[*].[name,platform,version,dateCreated,owner]' 2>/dev/null || echo "No Image Builder recipes found or access denied"

echo ""
echo "4. AMIs with Image Builder tags:"
echo "==============================="
aws ec2 describe-images \
    --owners self \
    --region "$REGION" \
    --filters "Name=tag-key,Values=*imagebuilder*,*ImageBuilder*,CreatedBy" \
    --query 'Images[*].[ImageId,Name,CreationDate,Platform,Tags[?Key==`CreatedBy`].Value|[0]]' \
    --output table 2>/dev/null || echo "No AMIs with Image Builder tags found"

echo ""
echo "5. Detailed Image Builder Images with AMI IDs:"
echo "=============================================="
# Get detailed information about each Image Builder image
aws imagebuilder list-images --region "$REGION" --output json 2>/dev/null | jq -r '.imageVersionList[]?.arn' | while read -r image_arn; do
    if [ -n "$image_arn" ]; then
        echo "Image: $image_arn"
        aws imagebuilder get-image --image-build-version-arn "$image_arn" --region "$REGION" --output json 2>/dev/null | jq -r '.image.outputResources.amis[]? | "  AMI: \(.image) (\(.region))"' || echo "  No AMI output found"
        echo ""
    fi
done

echo "=== Check Complete ==="
