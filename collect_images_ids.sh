#!/bin/bash

# Simple script to collect all Image Builder AMI IDs from all regions
source .env

OUTPUT_FILE="imagebuilder-amis.txt"

# Get all AWS regions
REGIONS=$(aws ec2 describe-regions --query 'Regions[].RegionName' --output text)

# Clear output file
> $OUTPUT_FILE

# Process each region
for REGION in $REGIONS; do
    echo "Processing $REGION..."
    
    # Initialize next token for pagination
    NEXT_TOKEN=""
    
    # Loop through all pages of images
    while true; do
        # Build command with or without next-token
        if [ -z "$NEXT_TOKEN" ]; then
            RESPONSE=$(aws imagebuilder list-images --region $REGION --output json)
        else
            RESPONSE=$(aws imagebuilder list-images --region $REGION --next-token "$NEXT_TOKEN" --output json)
        fi
        
        # Extract image ARNs from current page
        echo "$RESPONSE" | jq -r '.imageVersionList[].arn' | \
        while read IMAGE_ARN; do
            if [ ! -z "$IMAGE_ARN" ]; then
                # Get image details and extract AMI IDs
                aws imagebuilder get-image --image-build-version-arn "$IMAGE_ARN" --region $REGION --output json | \
                jq -r --arg region "$REGION" '.image.outputResources.amis[]? | select(.region == $region) | .image' >> $OUTPUT_FILE
            fi
        done
        
        # Check if there's a next token
        NEXT_TOKEN=$(echo "$RESPONSE" | jq -r '.nextToken // empty')
        
        # Break if no more pages
        if [ -z "$NEXT_TOKEN" ]; then
            break
        fi
    done
done

# Remove duplicates and empty lines
sort $OUTPUT_FILE | uniq | grep -v '^$' > ${OUTPUT_FILE}.tmp
mv ${OUTPUT_FILE}.tmp $OUTPUT_FILE

echo "Done. AMI IDs saved to $OUTPUT_FILE"
echo "Total AMIs found: $(wc -l < $OUTPUT_FILE)"