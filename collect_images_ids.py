#!/usr/bin/env python3
"""
Script to collect all Image Builder AMI IDs from all regions in a single AWS account
using only the Image Builder service (no EC2 instance or tag information).

Author: AWS Config Query Assistant
Version: 1.0
"""

import boto3
import csv
import json
import logging
import argparse
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from botocore.exceptions import ClientError, NoCredentialsError
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(
            f'imagebuilder-collection-{datetime.now().strftime("%Y%m%d-%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ImageBuilderAMICollector:
    def __init__(self, output_file=None, max_workers=10):
        """Initialize the AMI collector"""
        self.session = boto3.Session()
        self.max_workers = max_workers
        self.timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        self.output_file = output_file or f'imagebuilder-amis-{self.timestamp}.csv'
        self.account_id = None
        self.all_amis = []

    def check_credentials(self):
        """Verify AWS credentials are configured"""
        try:
            sts = self.session.client('sts')
            response = sts.get_caller_identity()
            self.account_id = response['Account']
            logger.info(f"Using AWS Account: {self.account_id}")
            return True
        except NoCredentialsError:
            logger.error("AWS credentials not configured")
            return False
        except Exception as e:
            logger.error(f"Error checking credentials: {e}")
            return False

    def get_all_regions(self):
        """Get list of all AWS regions"""
        try:
            ec2 = self.session.client('ec2', region_name='us-east-1')
            response = ec2.describe_regions()
            regions = [region['RegionName'] for region in response['Regions']]
            logger.info(f"Found {len(regions)} AWS regions")
            return regions
        except Exception as e:
            logger.error(f"Error getting regions: {e}")
            return []

    def get_imagebuilder_images_in_region(self, region):
        """Get Image Builder images from a specific region"""
        amis = []
        try:
            imagebuilder = self.session.client(
                'imagebuilder', region_name=region)

            # Get all images with pagination
            paginator = imagebuilder.get_paginator('list_images')

            for page in paginator.paginate():
                for image_version in page.get('imageVersionList', []):
                    try:
                        # Get detailed image information
                        image_detail = imagebuilder.get_image(
                            imageBuildVersionArn=image_version['arn']
                        )

                        image_info = image_detail['image']

                        # Extract AMI information from output resources
                        if 'outputResources' in image_info and 'amis' in image_info['outputResources']:
                            for ami in image_info['outputResources']['amis']:
                                # Only collect AMIs in the current region
                                if ami.get('region') == region:
                                    ami_data = {
                                        'ami_id': ami.get('image', ''),
                                        'region': region,
                                        'ami_name': ami.get('name', ''),
                                        'ami_description': ami.get('description', ''),
                                        'image_name': image_info.get('name', ''),
                                        'image_version': image_info.get('version', ''),
                                        'image_arn': image_version.get('arn', ''),
                                        'image_build_version_arn': image_info.get('arn', ''),
                                        'date_created': image_info.get('dateCreated', ''),
                                        'platform': image_info.get('platform', ''),
                                        'os_version': image_info.get('osVersion', ''),
                                        'enhanced_image_metadata_enabled': image_info.get('enhancedImageMetadataEnabled', False),
                                        'image_recipe_arn': image_info.get('imageRecipe', {}).get('arn', ''),
                                        'infrastructure_configuration_arn': image_info.get('infrastructureConfiguration', {}).get('arn', ''),
                                        'distribution_configuration_arn': image_info.get('distributionConfiguration', {}).get('arn', ''),
                                        'image_tests_configuration': json.dumps(image_info.get('imageTestsConfiguration', {})),
                                        'build_type': image_info.get('buildType', ''),
                                        'image_source': image_info.get('imageSource', ''),
                                        'account_id': self.account_id,
                                        'ami_state': ami.get('state', {}).get('status', ''),
                                        'ami_state_reason': ami.get('state', {}).get('reason', '')
                                    }

                                    # Add container recipe information if available
                                    if 'containerRecipe' in image_info:
                                        ami_data['container_recipe_arn'] = image_info['containerRecipe'].get(
                                            'arn', '')
                                    else:
                                        ami_data['container_recipe_arn'] = ''

                                    # Add tags if available
                                    if 'tags' in image_info:
                                        ami_data['image_tags'] = json.dumps(
                                            image_info['tags'])
                                    else:
                                        ami_data['image_tags'] = '{}'

                                    amis.append(ami_data)

                    except Exception as e:
                        logger.warning(
                            f"Error getting image details for {image_version.get('arn', 'unknown')} in {region}: {e}")
                        continue

            logger.info(f"Found {len(amis)} Image Builder AMIs in {region}")

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code in ['AccessDenied', 'UnauthorizedOperation']:
                logger.warning(
                    f"No permission to access Image Builder in {region}")
            elif error_code in ['InvalidAction', 'UnsupportedOperation']:
                logger.warning(
                    f"Image Builder service not available in {region}")
            else:
                logger.warning(
                    f"Error accessing Image Builder in {region}: {e}")
        except Exception as e:
            logger.warning(f"Unexpected error in {region}: {e}")

        return amis

    def get_imagebuilder_pipelines_in_region(self, region):
        """Get Image Builder pipelines information for context"""
        pipelines = []
        try:
            imagebuilder = self.session.client(
                'imagebuilder', region_name=region)

            # Get all pipelines with pagination
            paginator = imagebuilder.get_paginator('list_image_pipelines')

            for page in paginator.paginate():
                pipelines.extend(page.get('imagePipelineList', []))

            logger.info(
                f"Found {len(pipelines)} Image Builder pipelines in {region}")

        except Exception as e:
            logger.warning(f"Error getting pipelines in {region}: {e}")

        return pipelines

    def process_region(self, region):
        """Process a single region to find all Image Builder AMIs"""
        logger.info(f"Processing region: {region}")

        # Get AMIs from Image Builder service only
        imagebuilder_amis = self.get_imagebuilder_images_in_region(region)

        # Get pipeline information for context (optional)
        pipelines = self.get_imagebuilder_pipelines_in_region(region)

        # Add pipeline context to AMIs if possible
        pipeline_map = {}
        for pipeline in pipelines:
            pipeline_name = pipeline.get('name', '')
            pipeline_arn = pipeline.get('arn', '')
            if pipeline_name:
                pipeline_map[pipeline_name] = pipeline_arn

        # Enhance AMI data with pipeline information
        for ami in imagebuilder_amis:
            image_name = ami.get('image_name', '')
            # Try to match pipeline by name similarity
            matching_pipeline_arn = ''
            for pipeline_name, pipeline_arn in pipeline_map.items():
                if pipeline_name in image_name or image_name in pipeline_name:
                    matching_pipeline_arn = pipeline_arn
                    break
            ami['related_pipeline_arn'] = matching_pipeline_arn

        logger.info(
            f"Region {region}: {len(imagebuilder_amis)} Image Builder AMIs found")
        return imagebuilder_amis

    def collect_all_amis(self):
        """Collect AMIs from all regions using parallel processing"""
        regions = self.get_all_regions()
        if not regions:
            logger.error("No regions found")
            return False

        logger.info(
            f"Starting parallel collection across {len(regions)} regions")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit tasks for all regions
            future_to_region = {
                executor.submit(self.process_region, region): region
                for region in regions
            }

            # Collect results
            for future in as_completed(future_to_region):
                region = future_to_region[future]
                try:
                    region_amis = future.result()
                    self.all_amis.extend(region_amis)
                except Exception as e:
                    logger.error(f"Error processing region {region}: {e}")

        logger.info(
            f"Collection complete. Total AMIs found: {len(self.all_amis)}")
        return True

    def save_to_csv(self):
        """Save collected AMIs to CSV file"""
        if not self.all_amis:
            logger.warning("No AMIs to save")
            return False

        # Define CSV headers in logical order
        headers = [
            'ami_id',
            'region',
            'ami_name',
            'ami_description',
            'ami_state',
            'ami_state_reason',
            'image_name',
            'image_version',
            'image_arn',
            'image_build_version_arn',
            'date_created',
            'platform',
            'os_version',
            'build_type',
            'image_source',
            'enhanced_image_metadata_enabled',
            'image_recipe_arn',
            'container_recipe_arn',
            'infrastructure_configuration_arn',
            'distribution_configuration_arn',
            'related_pipeline_arn',
            'image_tests_configuration',
            'image_tags',
            'account_id'
        ]

        try:
            with open(self.output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=headers)

                # Write header
                writer.writeheader()

                # Write data
                for ami in self.all_amis:
                    # Ensure all fields exist
                    row = {header: ami.get(header, '') for header in headers}
                    writer.writerow(row)

            logger.info(
                f"Successfully saved {len(self.all_amis)} AMIs to {self.output_file}")
            return True

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return False

    def save_simple_list(self, filename=None):
        """Save just the AMI IDs to a simple text file"""
        if not self.all_amis:
            return False

        if not filename:
            filename = f'imagebuilder-ami-ids-{self.timestamp}.txt'

        try:
            with open(filename, 'w') as f:
                f.write(
                    f"# Image Builder AMI IDs collected on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Account: {self.account_id}\n")
                f.write(f"# Total AMIs: {len(self.all_amis)}\n")
                f.write("#\n")

                for ami in sorted(self.all_amis, key=lambda x: (x['region'], x['ami_id'])):
                    f.write(f"{ami['ami_id']}\n")

            logger.info(f"Saved simple AMI ID list to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error saving simple list: {e}")
            return False

    def generate_summary(self):
        """Generate and display summary statistics"""
        if not self.all_amis:
            print("\n=== IMAGE BUILDER AMI COLLECTION SUMMARY ===")
            print("No Image Builder AMIs found in this account")
            return

        # Calculate statistics
        regions_with_amis = len(set(ami['region'] for ami in self.all_amis))
        amis_by_region = {}
        amis_by_platform = {}
        amis_by_state = {}
        amis_by_build_type = {}

        for ami in self.all_amis:
            region = ami['region']
            platform = ami['platform']
            state = ami['ami_state']
            build_type = ami['build_type']

            amis_by_region[region] = amis_by_region.get(region, 0) + 1
            amis_by_platform[platform] = amis_by_platform.get(platform, 0) + 1
            amis_by_state[state] = amis_by_state.get(state, 0) + 1
            amis_by_build_type[build_type] = amis_by_build_type.get(
                build_type, 0) + 1

        print("\n=== IMAGE BUILDER AMI COLLECTION SUMMARY ===")
        print(f"Account ID: {self.account_id}")
        print(
            f"Collection Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total AMIs Found: {len(self.all_amis)}")
        print(f"Regions with AMIs: {regions_with_amis}")
        print(f"Output CSV File: {self.output_file}")

        print(f"\nAMIs by Region:")
        for region, count in sorted(amis_by_region.items(), key=lambda x: x[1], reverse=True):
            print(f"  {region}: {count}")

        print(f"\nAMIs by Platform:")
        for platform, count in amis_by_platform.items():
            if platform:  # Only show non-empty platforms
                print(f"  {platform}: {count}")

        print(f"\nAMIs by State:")
        for state, count in amis_by_state.items():
            if state:  # Only show non-empty states
                print(f"  {state}: {count}")

        if amis_by_build_type:
            print(f"\nAMIs by Build Type:")
            for build_type, count in amis_by_build_type.items():
                if build_type:  # Only show non-empty build types
                    print(f"  {build_type}: {count}")

        # Show sample AMIs
        if len(self.all_amis) > 0:
            print(f"\nSample AMIs (first 5):")
            for i, ami in enumerate(self.all_amis[:5]):
                image_name = ami['image_name'] or 'Unnamed'
                version = ami['image_version'] or 'No version'
                print(
                    f"  {i+1}. {ami['ami_id']} ({ami['region']}) - {image_name} v{version}")

        # Show unique image names
        unique_images = set(ami['image_name']
                            for ami in self.all_amis if ami['image_name'])
        if unique_images:
            print(f"\nUnique Image Builder Images ({len(unique_images)}):")
            for image_name in sorted(unique_images):
                count = sum(
                    1 for ami in self.all_amis if ami['image_name'] == image_name)
                print(f"  {image_name}: {count} AMIs")

        print(f"\n✅ Detailed data saved to: {self.output_file}")

        # Save simple list
        simple_filename = f'imagebuilder-ami-ids-{self.timestamp}.txt'
        if self.save_simple_list(simple_filename):
            print(f"✅ Simple AMI ID list saved to: {simple_filename}")

    def run(self):
        """Main execution method"""
        print("Image Builder AMI Collection Script")
        print("==================================")
        print("Collecting AMIs from Image Builder service only")
        print()

        # Check credentials
        if not self.check_credentials():
            return False

        # Collect AMIs
        if not self.collect_all_amis():
            return False

        # Save to CSV
        if not self.save_to_csv():
            return False

        # Generate summary
        self.generate_summary()

        return True


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description='Collect all Image Builder AMI IDs from all AWS regions and save as CSV'
    )
    parser.add_argument(
        '-o', '--output',
        help='Output CSV file name',
        default=None
    )
    parser.add_argument(
        '--max-workers',
        type=int,
        default=10,
        help='Maximum number of parallel workers (default: 10)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create collector and run
    collector = ImageBuilderAMICollector(
        output_file=args.output,
        max_workers=args.max_workers
    )

    success = collector.run()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
