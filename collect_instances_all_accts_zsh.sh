#!/bin/zsh
# Enhanced zsh script to collect instances across all accounts in organization
# Uses zsh-specific features for better performance and error handling

# Enable zsh options for better scripting
setopt ERR_EXIT          # Exit on error
setopt PIPE_FAIL         # Fail on pipe errors
setopt NO_UNSET          # Error on undefined variables
setopt EXTENDED_GLOB     # Enable extended globbing

# Configuration
typeset -r SCRIPT_NAME=${0:t}
typeset -r TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
typeset -r ACCOUNTS_FILE="accounts.txt"
typeset -r RESULTS_FILE="instances_report_${TIMESTAMP}.csv"
typeset -r LOG_FILE="collection_log_${TIMESTAMP}.log"

# AWS Configuration
typeset -r AWS_PROFILE="nxp-master"
typeset -ra REGIONS=(us-east-1 us-west-2 eu-west-1)
typeset -r ROLE_NAME="OrganizationAccountAccessRole"
typeset -r SESSION_NAME="AMIAudit-${TIMESTAMP}"

# Colors for output (zsh supports these natively)
typeset -r RED=$'\e[31m'
typeset -r GREEN=$'\e[32m'
typeset -r YELLOW=$'\e[33m'
typeset -r BLUE=$'\e[34m'
typeset -r RESET=$'\e[0m'

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo "${GREEN}[INFO]${RESET} $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo "${YELLOW}[WARN]${RESET} $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo "${RED}[ERROR]${RESET} $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo "${BLUE}[DEBUG]${RESET} $message" >> "$LOG_FILE" ;;
    esac
}

# Error handler
error_handler() {
    local exit_code=$?
    local line_number=$1
    log ERROR "Script failed at line $line_number with exit code $exit_code"
    exit $exit_code
}

# Set up error trapping
trap 'error_handler $LINENO' ERR

# Check prerequisites
check_prerequisites() {
    log INFO "Checking prerequisites..."
    
    # Check required commands
    local -a required_commands=(aws jq)
    for cmd in $required_commands; do
        if ! command -v $cmd &> /dev/null; then
            log ERROR "Required command '$cmd' not found"
            exit 1
        fi
    done
    
    # Check AWS CLI version
    local aws_version=$(aws --version 2>&1 | cut -d/ -f2 | cut -d' ' -f1)
    log INFO "AWS CLI version: $aws_version"
    
    log INFO "Prerequisites check passed"
}

# AWS SSO login
aws_sso_login() {
    log INFO "Performing AWS SSO login with profile: $AWS_PROFILE"
    
    if ! aws sso login --profile "$AWS_PROFILE" 2>> "$LOG_FILE"; then
        log ERROR "AWS SSO login failed"
        exit 1
    fi
    
    # Verify login
    local account_id=$(aws sts get-caller-identity --profile "$AWS_PROFILE" --query Account --output text 2>/dev/null)
    if [[ -n $account_id ]]; then
        log INFO "Successfully logged in to account: $account_id"
    else
        log ERROR "Failed to verify AWS login"
        exit 1
    fi
}

# Get organization accounts
get_accounts() {
    log INFO "Retrieving organization accounts..."
    
    if ! aws organizations list-accounts \
        --profile "$AWS_PROFILE" \
        --query 'Accounts[?Status==`ACTIVE`].[Id,Name]' \
        --output text > "$ACCOUNTS_FILE" 2>> "$LOG_FILE"; then
        log ERROR "Failed to retrieve organization accounts"
        exit 1
    fi
    
    local account_count=$(wc -l < "$ACCOUNTS_FILE")
    log INFO "Found $account_count active accounts"
}

# Assume cross-account role
assume_role() {
    local account_id=$1
    local role_arn="arn:aws:iam::${account_id}:role/${ROLE_NAME}"
    
    log DEBUG "Assuming role: $role_arn"
    
    aws sts assume-role \
        --profile "$AWS_PROFILE" \
        --role-arn "$role_arn" \
        --role-session-name "$SESSION_NAME" \
        --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' \
        --output text 2>> "$LOG_FILE"
}

# Collect instances from a single account and region
collect_instances() {
    local account_id=$1
    local account_name=$2
    local region=$3
    local access_key=$4
    local secret_key=$5
    local session_token=$6
    
    log DEBUG "Collecting instances from $account_name ($account_id) in $region"
    
    # Set temporary credentials
    export AWS_ACCESS_KEY_ID="$access_key"
    export AWS_SECRET_ACCESS_KEY="$secret_key"
    export AWS_SESSION_TOKEN="$session_token"
    
    # Get instances with enhanced query
    local instances_data
    instances_data=$(aws ec2 describe-instances \
        --region "$region" \
        --query 'Reservations[].Instances[].[InstanceId,ImageId,InstanceType,State.Name,Tags[?Key==`Name`].Value|[0],LaunchTime,Platform]' \
        --output text 2>> "$LOG_FILE") || {
        log WARN "Failed to get instances for $account_name in $region"
        return 1
    }
    
    # Process instances data
    if [[ -n $instances_data ]]; then
        local instance_count=0
        while IFS=$'\t' read -r instance_id ami instance_type state name launch_time platform; do
            # Clean up fields
            name=${name:-"N/A"}
            platform=${platform:-"linux"}
            launch_time=${launch_time:-"N/A"}
            
            # Write to CSV (escape commas in names)
            name=${name//,/;}
            account_name_clean=${account_name//,/;}
            
            echo "$account_id,$account_name_clean,$region,$instance_id,$ami,$instance_type,$state,$name,$launch_time,$platform" >> "$RESULTS_FILE"
            ((instance_count++))
        done <<< "$instances_data"
        
        log DEBUG "Found $instance_count instances in $account_name ($region)"
    else
        log DEBUG "No instances found in $account_name ($region)"
    fi
    
    # Clean up credentials
    unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
}

# Process a single account
process_account() {
    local account_id=$1
    local account_name=$2
    
    log INFO "Processing account: $account_name ($account_id)"
    
    # Assume cross-account role
    local credentials
    credentials=$(assume_role "$account_id")
    
    if [[ $? -ne 0 ]] || [[ -z $credentials ]]; then
        log WARN "Failed to assume role for account $account_id"
        return 1
    fi
    
    # Parse credentials using zsh array features
    local -a cred_array
    cred_array=(${(s: :)credentials})
    
    if [[ ${#cred_array} -ne 3 ]]; then
        log WARN "Invalid credentials format for account $account_id"
        return 1
    fi
    
    local access_key=$cred_array[1]
    local secret_key=$cred_array[2]
    local session_token=$cred_array[3]
    
    # Process each region
    for region in $REGIONS; do
        log DEBUG "Processing region: $region"
        collect_instances "$account_id" "$account_name" "$region" "$access_key" "$secret_key" "$session_token"
    done
}

# Main execution
main() {
    log INFO "Starting $SCRIPT_NAME at $(date)"
    log INFO "Results will be saved to: $RESULTS_FILE"
    log INFO "Logs will be saved to: $LOG_FILE"
    
    # Initialize results file with enhanced header
    echo "AccountId,AccountName,Region,InstanceId,AMI,InstanceType,State,Name,LaunchTime,Platform" > "$RESULTS_FILE"
    
    # Run prerequisite checks
    check_prerequisites
    
    # AWS SSO login
    aws_sso_login
    
    # Get organization accounts
    get_accounts
    
    # Process accounts
    local total_accounts=0
    local processed_accounts=0
    local failed_accounts=0
    
    while IFS=$'\t' read -r account_id account_name; do
        # Skip empty lines
        [[ -z $account_id ]] && continue
        
        ((total_accounts++))
        
        if process_account "$account_id" "$account_name"; then
            ((processed_accounts++))
        else
            ((failed_accounts++))
        fi
        
        # Progress indicator
        log INFO "Progress: $processed_accounts/$total_accounts accounts processed"
        
    done < "$ACCOUNTS_FILE"
    
    # Generate summary
    local total_instances=$(( $(wc -l < "$RESULTS_FILE") - 1 ))  # Subtract header
    
    log INFO "Collection completed!"
    log INFO "Total accounts: $total_accounts"
    log INFO "Successfully processed: $processed_accounts"
    log INFO "Failed: $failed_accounts"
    log INFO "Total instances found: $total_instances"
    log INFO "Results saved to: $RESULTS_FILE"
    log INFO "Logs saved to: $LOG_FILE"
    
    # Show top AMIs if instances were found
    if [[ $total_instances -gt 0 ]]; then
        log INFO "Top 10 most used AMIs:"
        tail -n +2 "$RESULTS_FILE" | cut -d, -f5 | sort | uniq -c | sort -nr | head -10 | while read count ami; do
            log INFO "  $ami: $count instances"
        done
    fi
}

# Run main function
main "$@"
