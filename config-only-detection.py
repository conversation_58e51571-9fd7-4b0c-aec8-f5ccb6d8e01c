#!/usr/bin/env python3
import json

def is_imagebuilder_via_tags(resource):
    """Detect Image Builder resources using only tag information"""
    tags = resource.get('tags', [])
    
    imagebuilder_indicators = []
    
    for tag in tags:
        key = tag.get('key', '').lower()
        value = tag.get('value', '').lower()
        
        # Common Image Builder tags
        if key == 'createdby' and 'image builder' in value:
            imagebuilder_indicators.append(f"CreatedBy: {tag.get('value')}")
        
        if key in ['ec2imagebuilderarn', 'imagebuilderarn']:
            imagebuilder_indicators.append(f"ImageBuilder ARN tag: {key}")
        
        if 'imagebuilder' in key:
            imagebuilder_indicators.append(f"ImageBuilder in key: {key}")
        
        if 'imagebuilder' in value:
            imagebuilder_indicators.append(f"ImageBuilder in value: {value}")
        
        # Check for pipeline or recipe references
        if 'pipeline' in key and 'imagebuilder' in value:
            imagebuilder_indicators.append(f"Pipeline reference: {key}={value}")
    
    return len(imagebuilder_indicators) > 0, imagebuilder_indicators

def main():
    print("=== CONFIG-ONLY IMAGE BUILDER DETECTION ===")
    
    try:
        with open('all-ec2-instances.json', 'r') as f:
            data = json.load(f)
            instances = data.get('Results', [])
    except FileNotFoundError:
        print("Error: all-ec2-instances.json not found")
        return
    
    non_imagebuilder_instances = []
    imagebuilder_instances = []
    
    for instance in instances:
        ami_id = instance.get('configuration', {}).get('imageId')
        if not ami_id:
            continue
        
        is_ib, indicators = is_imagebuilder_via_tags(instance)
        
        if is_ib:
            imagebuilder_instances.append({
                'instance': instance,
                'indicators': indicators
            })
        else:
            non_imagebuilder_instances.append(instance)
    
    # Save results
    with open('config-only-non-imagebuilder-instances.json', 'w') as f:
        json.dump(non_imagebuilder_instances, f, indent=2)
    
    with open('config-only-imagebuilder-instances.json', 'w') as f:
        json.dump(imagebuilder_instances, f, indent=2)
    
    # Generate summary
    ami_usage = {}
    for instance in non_imagebuilder_instances:
        ami_id = instance.get('configuration', {}).get('imageId')
        if ami_id:
            ami_usage[ami_id] = ami_usage.get(ami_id, 0) + 1
    
    print(f"\n=== CONFIG-ONLY RESULTS ===")
    print(f"Total instances analyzed: {len(instances)}")
    print(f"Image Builder instances (by tags): {len(imagebuilder_instances)}")
    print(f"Non-Image Builder instances: {len(non_imagebuilder_instances)}")
    print(f"Unique non-Image Builder AMIs: {len(ami_usage)}")
    
    if imagebuilder_instances:
        print(f"\nImage Builder instances detected:")
        for item in imagebuilder_instances[:5]:
            instance = item['instance']
            instance_id = instance.get('resourceId')
            ami_id = instance.get('configuration', {}).get('imageId')
            print(f"  - {instance_id} using {ami_id}")
            for indicator in item['indicators']:
                print(f"    └ {indicator}")
    
    print(f"\nTop non-Image Builder AMIs:")
    for ami_id, count in sorted(ami_usage.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {ami_id}: {count} instances")

if __name__ == "__main__":
    main()