#!/bin/bash
# Script to collect instances across all accounts in organization
# set -x
aws sso login --profile nxp-master

# Get list of all accounts
aws organizations list-accounts --profile nxp-master --query 'Accounts[?Status==`ACTIVE`].[Id,Name]' --output text > accounts.txt

# Create results file
echo "AccountId,AccountName,Region,InstanceId,AMI,InstanceType,State,Tags" > instances_report.csv

# Get total count of accounts
total_accounts=$(wc -l < accounts.txt)

# Initialize account counter
account_counter=0

while read account_id account_name; do
    ((account_counter++))
    echo "Processing account $account_counter of $total_accounts: $account_name ($account_id)"
    
    # Assume cross-account role
    ROLE_ARN="arn:aws:iam::${account_id}:role/OrganizationAccountAccessRole"
    aws sts assume-role --profile nxp-master --role-arn $ROLE_ARN --role-session-name AMIAudit \
        --query 'Credentials.[<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>Acc<PERSON><PERSON><PERSON>,<PERSON>T<PERSON>]' \
        --output text | while read access_key secret_key session_token; do
            
            AWS_ACCESS_KEY_ID=$access_key \
            AWS_SECRET_ACCESS_KEY=$secret_key \
            AWS_SESSION_TOKEN=$session_token
            
            # Get all enabled regions for EC2
            enabled_regions=$(aws ec2 describe-regions --region eu-west-1 --query 'Regions[].RegionName' --output text)
            for region in $enabled_regions; do
                aws ec2 describe-instances --region $region \
                --query 'Reservations[].Instances[].[InstanceId,ImageId,InstanceType,State.Name,Tags[?Key==`Name`].Value|[0]]' \
                --output text | while read instance_id ami instance_type state name; do
                echo "$account_id,$account_name,$region,$instance_id,$ami,$instance_type,$state,$name" >> instances_report.csv
            done
        done
    done
done < accounts.txt