import boto3
import json
import csv
from datetime import datetime
from botocore.exceptions import ClientError, NoCredentialsError
import argparse
import sys


def assume_role(account_id, role_name="OrganizationAccountAccessRole"):
    """Assume cross-account role and return credentials"""
    try:
        sts_client = boto3.client('sts')
        role_arn = f"arn:aws:iam::{account_id}:role/{role_name}"

        response = sts_client.assume_role(
            RoleArn=role_arn,
            RoleSessionName=f"ImageBuilderAudit-{account_id}"
        )

        credentials = response['Credentials']
        return {
            'aws_access_key_id': credentials['AccessKeyId'],
            'aws_secret_access_key': credentials['SecretAccessKey'],
            'aws_session_token': credentials['SessionToken']
        }
    except ClientError as e:
        print(f"Error assuming role for account {account_id}: {e}")
        return None


def get_imagebuilder_images(session, region):
    """Get all images from EC2 Image Builder service"""
    try:
        imagebuilder_client = session.client(
            'imagebuilder', region_name=region)

        images = []
        paginator = imagebuilder_client.get_paginator('list_images')

        for page in paginator.paginate():
            for image_version in page.get('imageVersionList', []):
                # Get detailed image information
                try:
                    image_detail = imagebuilder_client.get_image(
                        imageBuildVersionArn=image_version['arn']
                    )
                    images.append({
                        'arn': image_version['arn'],
                        'name': image_version['name'],
                        'version': image_version['version'],
                        'platform': image_version['platform'],
                        'owner': image_version['owner'],
                        'date_created': image_version.get('dateCreated', ''),
                        'output_resources': image_detail['image'].get('outputResources', {}),
                        'region': region
                    })
                except ClientError as e:
                    print(
                        f"Error getting image details for {image_version['arn']}: {e}")

        return images
    except ClientError as e:
        if e.response['Error']['Code'] == 'AccessDenied':
            print(f"Access denied to Image Builder in region {region}")
        else:
            print(f"Error accessing Image Builder in region {region}: {e}")
        return []


def get_imagebuilder_amis_by_tags(session, region):
    """Get AMIs that have Image Builder related tags"""
    try:
        ec2_client = session.client('ec2', region_name=region)

        # Search for AMIs with Image Builder indicators
        filters = [
            {'Name': 'state', 'Values': ['available']},
            {'Name': 'tag-key',
                'Values': ['*imagebuilder*', '*ImageBuilder*', 'CreatedBy']}
        ]

        amis = []
        paginator = ec2_client.get_paginator('describe_images')

        for page in paginator.paginate(Owners=['self'], Filters=filters):
            for ami in page.get('Images', []):
                # Check if tags indicate Image Builder
                is_imagebuilder = False
                imagebuilder_indicators = []

                for tag in ami.get('Tags', []):
                    key = tag.get('Key', '').lower()
                    value = tag.get('Value', '').lower()

                    if ('imagebuilder' in key or 'imagebuilder' in value or
                            (key == 'createdby' and 'image builder' in value)):
                        is_imagebuilder = True
                        imagebuilder_indicators.append(
                            f"{tag['Key']}={tag['Value']}")

                if is_imagebuilder:
                    amis.append({
                        'ami_id': ami['ImageId'],
                        'name': ami.get('Name', ''),
                        'description': ami.get('Description', ''),
                        'creation_date': ami.get('CreationDate', ''),
                        'owner_id': ami.get('OwnerId', ''),
                        'platform': ami.get('Platform', 'linux'),
                        'architecture': ami.get('Architecture', ''),
                        'tags': ami.get('Tags', []),
                        'imagebuilder_indicators': imagebuilder_indicators,
                        'region': region
                    })

        return amis
    except ClientError as e:
        print(f"Error getting AMIs in region {region}: {e}")
        return []


def process_account(account_id, account_name, regions):
    """Process a single AWS account"""
    print(f"Processing account: {account_name} ({account_id})")

    # Assume role for cross-account access
    credentials = assume_role(account_id)
    if not credentials:
        return [], []

    session = boto3.Session(**credentials)

    all_imagebuilder_images = []
    all_imagebuilder_amis = []

    for region in regions:
        print(f"  Checking region: {region}")

        # Get Image Builder images
        ib_images = get_imagebuilder_images(session, region)
        for img in ib_images:
            img['account_id'] = account_id
            img['account_name'] = account_name
        all_imagebuilder_images.extend(ib_images)

        # Get AMIs with Image Builder tags
        ib_amis = get_imagebuilder_amis_by_tags(session, region)
        for ami in ib_amis:
            ami['account_id'] = account_id
            ami['account_name'] = account_name
        all_imagebuilder_amis.extend(ib_amis)

    return all_imagebuilder_images, all_imagebuilder_amis


def extract_ami_ids_from_imagebuilder_images(imagebuilder_images):
    """Extract AMI IDs from Image Builder output resources"""
    ami_ids = set()

    for image in imagebuilder_images:
        output_resources = image.get('output_resources', {})
        amis = output_resources.get('amis', [])

        for ami in amis:
            if 'image' in ami:
                ami_ids.add(ami['image'])

    return ami_ids


def main():
    parser = argparse.ArgumentParser(
        description='Find all images created by EC2 Image Builder')
    parser.add_argument('--regions', nargs='+', default=['us-east-1', 'us-west-2', 'eu-west-1'],
                        help='AWS regions to check (default: us-east-1 us-west-2 eu-west-1)')
    parser.add_argument('--accounts-file', default='accounts.txt',
                        help='File containing account IDs and names (default: accounts.txt)')
    parser.add_argument('--output-prefix', default='imagebuilder_audit',
                        help='Prefix for output files (default: imagebuilder_audit)')

    args = parser.parse_args()

    # Read accounts file
    try:
        with open(args.accounts_file, 'r') as f:
            accounts = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        accounts.append((parts[0], parts[1]))
    except FileNotFoundError:
        print(f"Error: {args.accounts_file} not found")
        sys.exit(1)

    # Limit to first 3 accounts for testing
    accounts = accounts[:3]

    print(
        f"Found {len(accounts)} accounts to process (limited to first 3 for testing)")
    print(f"Regions to check: {', '.join(args.regions)}")

    all_imagebuilder_images = []
    all_imagebuilder_amis = []

    # Process each account
    for account_id, account_name in accounts:
        try:
            ib_images, ib_amis = process_account(
                account_id, account_name, args.regions)
            all_imagebuilder_images.extend(ib_images)
            all_imagebuilder_amis.extend(ib_amis)
        except Exception as e:
            print(
                f"Error processing account {account_name} ({account_id}): {e}")
            continue

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save Image Builder images
    ib_images_file = f"{args.output_prefix}_images_{timestamp}.json"
    with open(ib_images_file, 'w') as f:
        json.dump(all_imagebuilder_images, f, indent=2, default=str)

    # Save Image Builder AMIs
    ib_amis_file = f"{args.output_prefix}_amis_{timestamp}.json"
    with open(ib_amis_file, 'w') as f:
        json.dump(all_imagebuilder_amis, f, indent=2, default=str)

    # Create CSV report
    csv_file = f"{args.output_prefix}_report_{timestamp}.csv"
    with open(csv_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Type', 'Account ID', 'Account Name', 'Region',
                        'Resource ID', 'Name', 'Creation Date', 'Platform', 'Details'])

        # Write Image Builder images
        for img in all_imagebuilder_images:
            ami_ids = []
            for ami in img.get('output_resources', {}).get('amis', []):
                if 'image' in ami:
                    ami_ids.append(ami['image'])

            writer.writerow([
                'ImageBuilder Image',
                img['account_id'],
                img['account_name'],
                img['region'],
                img['arn'].split('/')[-1],  # Extract image name from ARN
                img['name'],
                img['date_created'],
                img['platform'],
                f"AMIs: {', '.join(ami_ids)}"
            ])

        # Write AMIs with Image Builder tags
        for ami in all_imagebuilder_amis:
            writer.writerow([
                'AMI (Tagged)',
                ami['account_id'],
                ami['account_name'],
                ami['region'],
                ami['ami_id'],
                ami['name'],
                ami['creation_date'],
                ami['platform'],
                f"Indicators: {', '.join(ami['imagebuilder_indicators'])}"
            ])

    # Print summary
    print(f"\n=== SUMMARY ===")
    print(f"Image Builder Images found: {len(all_imagebuilder_images)}")
    print(f"AMIs with Image Builder tags: {len(all_imagebuilder_amis)}")

    # Extract AMI IDs from Image Builder images
    imagebuilder_ami_ids = extract_ami_ids_from_imagebuilder_images(
        all_imagebuilder_images)
    tagged_ami_ids = {ami['ami_id'] for ami in all_imagebuilder_amis}

    print(f"Unique AMI IDs from Image Builder: {len(imagebuilder_ami_ids)}")
    print(f"Unique AMI IDs from tags: {len(tagged_ami_ids)}")
    print(
        f"AMI IDs in both sources: {len(imagebuilder_ami_ids & tagged_ami_ids)}")

    print(f"\nOutput files:")
    print(f"  - {ib_images_file}")
    print(f"  - {ib_amis_file}")
    print(f"  - {csv_file}")


if __name__ == "__main__":
    main()
